import { useGetBeds } from "../../../server-action/API/HotelConfiguration/bed";
import { useGetAllPackages } from "../../../server-action/API/HotelConfiguration/roompackage";
import { useGetAllRoom } from "../../../server-action/API/Room/room";
import { useGetAllRoomType } from "../../../server-action/API/Room/room-type";
import { useGetAllUser } from "../../../server-action/API/user";

export const Reserve = (
  selectedRoomCategory: string,
  bedCategory: string,
  ac: string | boolean
) => {
  const { data: bedData } = useGetBeds();
  const { data: packageData } = useGetAllPackages();
  const { data: users } = useGetAllUser({ role: "guest" });

  const packageOptions = [
    { value: "all", label: "All" },
    ...(packageData?.map((pack) => ({
      value: pack._id,
      label: pack.name,
    })) || []),
  ];
  const bedOptions = [
    { value: "all", label: "All" },
    ...(bedData?.map((bed) => ({
      value: bed._id,
      label: bed.name,
    })) || []),
  ];

  const { data: roomTypes } = useGetAllRoomType();
  const roomTypeOptions = [
    { value: "all", label: "All" },
    ...(roomTypes?.map((roomType) => ({
      value: roomType._id,
      label: roomType.name,
    })) || []),
  ];

  const acOptions = [
    { value: "all", label: "All" },
    { value: "none", label: "None" },
    { value: "central", label: "Central" },
    { value: "split", label: "Split" },
    { value: "window", label: "Window" },
  ];

  // Create filter object for room query
  const roomFilters: Record<string, string | boolean> = {};

  // Only add filters if they have valid values
  if (selectedRoomCategory && selectedRoomCategory !== "all") {
    roomFilters.roomType = selectedRoomCategory;
  }

  if (ac && ac !== "all") {
    roomFilters.acType = ac;
  }

  if (bedCategory && bedCategory !== "all") {
    roomFilters.bedTypes = bedCategory;
  }

  console.log("Room filters:", roomFilters);

  // Fetch rooms with the applied filters
  const { data: roomData } = useGetAllRoom(roomFilters);

  console.log("roomData", roomData);

  // Map room data to options format
  const roomOptions = [
    ...(roomData?.map((room) => ({
      value: room._id,
      label: room.roomNo,
    })) || []),
  ];

  // Create user options for existing guests
  const userOptions =
    users?.map((user: any) => ({
      value: user._id,
      label: `${user.name} ${user.phoneNumber ? `(${user.phoneNumber})` : ""}`,
    })) || [];

  const ReservationDetails = [
    {
      label: "Check In",
      field: "checkin",
      type: "date",
    },
    {
      label: "Check Out",
      field: "checkout",
      type: "date",
    },
    {
      label: "Expected Check Out",
      field: "expectedCheckout",
      type: "date",
    },
    {
      label: "Reservation Date",
      field: "reservationDate",
      type: "date",
    },
    {
      label: "Package Type",
      field: "packageType",
      placeholder: "Select Package",
      type: "select",
      options: packageOptions,
    },
    {
      label: "A/C Type",
      field: "ac",
      placeholder: "Select A/C Type",
      type: "select",
      options: acOptions,
    },
    {
      label: "Bed Category",
      field: "bedCategory",
      placeholder: "Select Bed Category",
      type: "select",
      options: bedOptions,
    },
    {
      label: "Room Category",
      field: "roomCategory",
      placeholder: "Select Room Category",
      type: "select",
      options: roomTypeOptions,
    },
    {
      placeholder: "Select RoomNo",
      label: "Room No.",
      field: "roomNo",

      type: "select",
      options: roomOptions,
    },
  ];

  return { ReservationDetails };
};

// export

export const CustomerDetails = [
  {
    label: "Guest Name",
    field: "name", // Changed from guestName to name to match the validation schema
    type: "text",
  },
  {
    label: "Mobile No.",
    field: "mobileNo",
    type: "text",
  },
  {
    label: "Date of Birth",
    field: "dob",
    type: "date",
  },
  {
    label: "Gender",
    field: "gender",
    type: "select",
    placeholder: "Select Gender",
    options: [
      { value: "male", label: "Male" },
      { value: "female", label: "Female" },
      { value: "other", label: "Other" },
    ],
  },
  {
    label: "Email",
    field: "email",
    type: "email",
  },
  {
    label: "District",
    field: "permanentAddress.district",
    type: "text",
    placeholder: "Enter District",
  },
  {
    label: "Municipality",
    field: "permanentAddress.municipality",
    type: "text",
    placeholder: "Enter Municipality",
  },
  {
    label: "Tole",
    field: "permanentAddress.tole",
    type: "text",
    placeholder: "Enter Tole",
  },
  {
    label: "Country",
    field: "permanentAddress.country",
    type: "select",
    placeholder: "Select Country",
    options: [{ value: "Nepal", label: "Nepal" }],
  },
  // Guest Count Section
  {
    label: "Number of Guests",
    field: "numberOfGuest",
    type: "number",
    min: 1,
    max: 10,
    description: "Total number of guests (adults + children)",
    readOnly: true, // Make this read-only as it will be calculated
  },
  {
    label: "Adults",
    field: "adults",
    type: "number",
    min: 1,
    max: 10,
    description: "Number of adults (age 12+)",
  },
  {
    label: "Children",
    field: "children",
    type: "number",
    min: 0,
    max: 9,
    description: "Number of children (under age 12)",
  },
];

export const IdentityDetails = [
  // {
  //   label: "Identity Type",
  //   field: "identityType",
  //   type: "select",
  //   options: [
  //     { value: "driving_license", label: "Driving License" },
  //     { value: "national_identity", label: "National Identity" },
  //     { value: "passport", label: "Passport" },
  //     { value: "citizenship", label: "Citizenship" },
  //     { value: "others ", label: "Other" },
  //   ],
  // },
  // {
  //   label: "ID No.",
  //   field: "idNo",
  //   type: "text",
  // },
  // {
  //   label: "Upload Photo",
  //   field: "uploadPhoto",
  //   type: "files",
  // },
];

export const PaymentDetails = [
  {
    label: "Payment Type",
    field: "paymentType",
    placeholder: "Select Payment Type",
    type: "select",
    options: [
      { value: "partical", label: "Partial" },
      { value: "advance", label: "Advance" },
      { value: "final", label: "Final" },
    ],
  },
  {
    label: "Payment Method",
    field: "paymentMethod",
    placeholder: "Select Payment Method",
    type: "select",
    options: [
      { value: "cash", label: "Cash" },
      { value: "online", label: "Digital Wallet" },
      { value: "bank", label: "Bank Transfer" },
    ],
  },
  {
    label: "Total Amount",
    field: "totalAmount",
    type: "number",
    readOnly: true, // Make read-only since it's calculated
    description: "Total amount after any applicable membership discounts",
  },
  {
    label: "Paid Amount",
    field: "paidAmount",
    type: "number",
    min: 0,
    description: "Amount paid by guest",
  },
];
