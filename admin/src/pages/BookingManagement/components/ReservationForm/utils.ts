import moment from "moment";
import { get } from "lodash";
import { BookingStatus } from "../../../../Interface/booking.interface";

export const getInitialValues = (sections: any) => {
  return sections.reduce((values: Record<string, any>, section: any) => {
    section.fields.forEach((field: any) => {
      if (field.type === "files") {
        values[field.field] = [];
      } else if (field.type === "file") {
        values[field.field] = "";
      } else if (field.field.includes("permanentAddress")) {
        values.permanentAddress = { district: "", municipality: "", tole: "", country: "" };
      } else {
        values[field.field] = "";
      }
    });
    return values;
  }, {} as Record<string, any>);
};

export const formatDate = (date: any) => {
  if (!date) return "";
  return moment(date).format("YYYY-MM-DD");
};

export const getEditModeInitialValues = (editData: any) => {
  return {
    checkin: moment(get(editData, "checkIn")).format("YYYY-MM-DD"),
    checkout: get(editData, "checkOut")
      ? moment(get(editData, "checkOut")).format("YYYY-MM-DD")
      : "",
    expectedCheckout: get(editData, "expectedCheckOut")
      ? moment(get(editData, "expectedCheckOut")).format("YYYY-MM-DD")
      : "",
    reservationDate: get(editData, "reservationDate")
      ? moment(get(editData, "reservationDate")).format("YYYY-MM-DD")
      : "",
    isExistingGuest: false,
    existingGuestId: get(editData, "guest._id") || "",
    packageType: get(editData, "package.package") || "",
    ac: "",
    bedCategory: "",
    roomCategory: get(editData, "roomType._id") || "",
    roomNo: get(editData, "room._id"),
    name: get(editData, "guest.name"),
    guestName: get(editData, "guest.name"),
    mobileNo: get(editData, "guest.phoneNumber"),
    dob: moment(get(editData, "guest.DOB")).format("YYYY-MM-DD"),
    gender: get(editData, "guest.gender"),
    email: get(editData, "guest.email"),
    permanentAddress: {
      district: get(editData, "guest.permanentAddress.district"),
      municipality: get(editData, "guest.permanentAddress.municipality"),
      tole: get(editData, "guest.permanentAddress.tole"),
      country: get(editData, "guest.permanentAddress.country"),
    },
    numberOfGuest:
      get(editData, "pax.adults") + get(editData, "pax.children", 0),
    adults: get(editData, "pax.adults", 1),
    children: get(editData, "pax.children", 0),
    documents: get(editData, "guest.documents", []),
    paymentType: get(editData, "paymentType"),
    totalAmount: get(editData, "amount"),
    paidAmount: get(editData, "amountPaid", 0),
    paymentMethod: get(editData, "paymentMethod"),
    status: get(editData, "status", BookingStatus.CONFIRMED),
  };
};

export const getAddModeInitialValues = (formSections: any) => {
  return {
    ...getInitialValues(formSections),
    isExistingGuest: false,
    existingGuestId: "",
    name: "",
    guestName: "",
    expectedCheckout: "",
    reservationDate: "",
    totalAmount: 0,
    paidAmount: 0,
    adults: 1,
    children: 0,
    status: BookingStatus.CONFIRMED,
    documents: [
      {
        IdentityType: "",
        IdNo: "",
        images: [],
      },
    ],
  };
};

export const updateBookingStatus = (
  checkinDate: string | null,
  checkoutDate: string | null
): BookingStatus => {
  let newStatus = BookingStatus.CONFIRMED;

  if (checkinDate) {
    newStatus = BookingStatus.CHECKED_IN;
    if (checkoutDate) {
      newStatus = BookingStatus.CHECKED_OUT;
    }
  }

  return newStatus;
};
