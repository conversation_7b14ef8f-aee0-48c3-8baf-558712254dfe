import * as Yup from "yup";
export const getBookingValidationSchema = (isEditMode = false) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return Yup.object().shape({
    checkin: Yup.date().nullable().min(today, "Check-in cannot be in the past"),
    checkout: Yup.date()
      .nullable()
      .test(
        "checkout-after-or-equal-checkin",
        "Check-out date must be on or after check-in date",
        function (value, context) {
          const { checkin } = context.parent;
          return !checkin || !value || new Date(value) >= new Date(checkin);
        }
      ),
    expectedCheckout: Yup.date()
      .required("Expected checkout date is required")
      .test(
        "expected-checkout-after-or-equal-checkin",
        "Expected check-out date must be on or after check-in date",
        function (value, context) {
          const { checkin } = context.parent;
          return !checkin || new Date(value) >= new Date(checkin);
        }
      ),
    reservationDate: Yup.date().nullable(),
    roomCategory: Yup.string().required("Room category is required"),
    roomNo: Yup.string().required("Room number is required"),
    packageType: Yup.string().nullable(),
    ac: Yup.string().nullable(),
    bedCategory: Yup.string().nullable(),
    isExistingGuest: Yup.boolean(),
    existingGuestId: Yup.string().when("isExistingGuest", {
      is: true,
      then: (schema) => schema.required("Please select an existing guest"),
      otherwise: (schema) => schema.notRequired(),
    }),
    name: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: false,
          then: (schema) => schema.required("Guest name is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
    guestName: Yup.string().notRequired(),
    mobileNo: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: false,
          then: (schema) =>
            schema
              .required("Mobile number is required")
              .matches(/^\d{10,15}$/, "Invalid mobile number"),
          otherwise: (schema) => schema.notRequired(),
        }),
    dob: isEditMode
      ? Yup.date().notRequired()
      : Yup.date().when("isExistingGuest", {
          is: false,
          then: (schema) => schema.required("Date of birth is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
    gender: isEditMode
      ? Yup.string().notRequired()
      : Yup.string().when("isExistingGuest", {
          is: false,
          then: (schema) => schema.required("Gender is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
    email: Yup.string().email("Invalid email").nullable(),
    permanentAddress: Yup.object().shape({
      district: isEditMode
        ? Yup.string().notRequired()
        : Yup.string().when("isExistingGuest", {
            is: false,
            then: (schema) => schema.required("District is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
      municipality: isEditMode
        ? Yup.string().notRequired()
        : Yup.string().when("isExistingGuest", {
            is: false,
            then: (schema) => schema.required("Municipality is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
      tole: isEditMode
        ? Yup.string().notRequired()
        : Yup.string().when("isExistingGuest", {
            is: false,
            then: (schema) => schema.required("Tole is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
      country: isEditMode
        ? Yup.string().notRequired()
        : Yup.string().when("isExistingGuest", {
            is: false,
            then: (schema) => schema.required("Country is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
    }),
    numberOfGuest: Yup.number()
      .required("Total number of guests is required")
      .min(1, "At least one guest is required")
      .max(10, "Maximum 10 guests allowed")
      .test(
        "equals-sum",
        "Number of guests must equal sum of adults and children",
        function (value, context) {
          const { adults, children } = context.parent;
          return value === (Number(adults) || 0) + (Number(children) || 0);
        }
      ),
    adults: Yup.number()
      .required("Number of adults is required")
      .min(1, "At least one adult is required")
      .max(10, "Maximum 10 adults allowed"),
    children: Yup.number()
      .required("Number of children is required")
      .min(0, "Number of children cannot be negative")
      .max(9, "Maximum 9 children allowed"),
    documents: Yup.array().nullable(),
    paymentMethod: Yup.string().nullable(),
    totalAmount: Yup.number()
      .required("Total amount is required")
      .min(0, "Amount must be non-negative"),
    paidAmount: Yup.number()
      .required("Paid amount is required")
      .min(0, "Paid amount must be non-negative")
      .test(
        "paid-amount-not-greater-than-total",
        "Paid amount cannot exceed total amount",
        function (value, context) {
          const { totalAmount } = context.parent;
          return !value || !totalAmount || value <= totalAmount;
        }
      ),
  });
};
